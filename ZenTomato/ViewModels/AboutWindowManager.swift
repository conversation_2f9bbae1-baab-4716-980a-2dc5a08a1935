//
//  AboutWindowManager.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/13.
//  关于窗口管理器 - 管理关于页面的显示和窗口状态
//

import SwiftUI
import AppKit

/// 关于窗口管理器
class AboutWindowManager: NSObject, ObservableObject {
    // MARK: - Properties
    
    /// 关于窗口实例
    private var aboutWindow: NSWindow?
    
    /// 窗口是否已显示
    @Published var isWindowShown = false
    
    // MARK: - Singleton
    
    static let shared = AboutWindowManager()
    
    private override init() {
        super.init()
    }
    
    // MARK: - Public Methods
    
    /// 显示关于窗口
    func showAboutWindow() {
        // 如果窗口已存在且可见，将其置于前台
        if let window = aboutWindow, window.isVisible {
            window.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
            return
        }
        
        // 创建新窗口或重新显示已存在的窗口
        if aboutWindow == nil {
            createAboutWindow()
        }
        
        aboutWindow?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
        isWindowShown = true
    }
    
    /// 隐藏关于窗口
    func hideAboutWindow() {
        aboutWindow?.orderOut(nil)
        isWindowShown = false
    }
    
    /// 关闭关于窗口
    func closeAboutWindow() {
        aboutWindow?.close()
        aboutWindow = nil
        isWindowShown = false
    }
    
    // MARK: - Private Methods
    
    /// 创建关于窗口
    private func createAboutWindow() {
        // 创建关于视图
        let aboutView = AboutView()
        
        // 创建窗口内容视图控制器
        let hostingController = NSHostingController(rootView: aboutView)
        
        // 创建窗口
        aboutWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 300),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )
        
        // 配置窗口属性
        aboutWindow?.title = "关于 禅番茄"
        aboutWindow?.contentViewController = hostingController
        aboutWindow?.isReleasedWhenClosed = false
        aboutWindow?.center()
        
        // 设置窗口样式
        aboutWindow?.titlebarAppearsTransparent = true
        aboutWindow?.backgroundColor = NSColor.clear
        
        // 监听窗口关闭事件
        aboutWindow?.delegate = self
        
        // 设置窗口层级，确保显示在其他窗口之上
        aboutWindow?.level = .floating
    }
}

// MARK: - NSWindowDelegate

extension AboutWindowManager: NSWindowDelegate {
    /// 窗口即将关闭
    func windowWillClose(_ notification: Notification) {
        isWindowShown = false
    }
    
    /// 窗口应该关闭
    func windowShouldClose(_ sender: NSWindow) -> Bool {
        // 允许关闭窗口
        return true
    }
}
